name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: 3.12
      - run: |
          pip install uv
          uv sync --dev
          uv run pytest tests/ -v
          uv run ruff check mono_banking_mcp/ tests/

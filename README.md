# Mono Banking MCP Server

Simple **Model Context Protocol (MCP)** server for Nigerian banking operations using the [Mono Open Banking API](https://mono.co).

## Features

- Account management and balance checks
- Payment processing with DirectPay
- Transaction history and account verification
- BVN lookup for identity verification
- Nigerian bank directory

## Setup

### Prerequisites
- Python 3.12+
- Mono API credentials from [mono.co](https://mono.co)

### Installation
```bash
git clone <your-repo-url>
cd mono-banking-mcp
uv sync --dev  # or pip install -e ".[dev]"
```

### Configuration
Create `.env` file:
```env
MONO_SECRET_KEY=your_actual_mono_secret_key_here
MONO_WEBHOOK_SECRET=your_webhook_secret_here  # optional
```

### Run
```bash
uv run python -m mono_banking_mcp.server
# or: make server
```

## Claude Desktop Integration

Add to `~/.config/claude-desktop/config.json`:
```json
{
  "mcpServers": {
    "mono-banking": {
      "command": "uv",
      "args": ["run", "python", "-m", "mono_banking_mcp.server"],
      "cwd": "/path/to/mono-banking-mcp",
      "env": {
        "MONO_SECRET_KEY": "your_actual_mono_secret_key_here"
      }
    }
  }
}
```

## Available Tools

- `list_linked_accounts` - List all linked bank accounts
- `get_account_balance` - Get current account balance
- `get_account_details` - Get comprehensive account info with BVN
- `get_transaction_history` - Retrieve transaction records
- `verify_account_name` - Verify recipient account details
- `initiate_payment` - Start a payment via DirectPay
- `verify_payment` - Check payment status
- `lookup_bvn` - BVN identity verification
- `get_nigerian_banks` - List supported Nigerian banks
- `initiate_account_linking` - Start account linking process

## Development

```bash
make test     # run tests
make lint     # check code quality
make format   # format code
make tools    # list available MCP tools
```
